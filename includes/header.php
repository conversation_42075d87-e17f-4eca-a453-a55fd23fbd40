<?php
// ตรวจสอบว่าเริ่ม session ไม่
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// เรืองการเชื่อมต่อฐานข้อมูล
require_once __DIR__ . '/db.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Container System</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      background-color: #f8f9fa;
    }
    .navbar-brand {
      font-weight: bold;
    }
    .sidebar {
      min-height: calc(100vh - 56px);
      background-color: #343a40;
    }
    .sidebar .nav-link {
      color: rgba(255, 255, 255, 0.75);
    }
    .sidebar .nav-link:hover {
      color: #fff;
    }
    .sidebar .nav-link.active {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.1);
    }
    .sidebar .nav-link i {
      margin-right: 10px;
    }
    .content {
      padding: 20px;
    }
  </style>
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
  <div class="container-fluid">
    <a class="navbar-brand" href="dashboard.php">Container System</a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="job_list.php"><i class="fas fa-list"></i> Jobs</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="create_job.php"><i class="fas fa-plus"></i> Create Job</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="report.php"><i class="fas fa-chart-bar"></i> Reports</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="user_list.php"><i class="fas fa-users"></i> Users</a>
        </li>
      </ul>
      <ul class="navbar-nav ms-auto">
        <?php if (isset($_SESSION["username"])): ?>
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-user"></i> <?php echo htmlspecialchars($_SESSION["username"]); ?>
          </a>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-circle"></i> Profile</a></li>
            <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
          </ul>
        </li>
        <?php else: ?>
        <li class="nav-item">
          <a class="nav-link" href="login.php"><i class="fas fa-sign-in-alt"></i> Login</a>
        </li>
        <?php endif; ?>
      </ul>
    </div>
  </div>
</nav>
<div class="container-fluid mt-3">
  <div class="row">
    <div class="col-md-3 col-lg-2">
      <div class="sidebar p-3">
        <ul class="nav flex-column">
          <li class="nav-item">
            <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : '' ?>" href="dashboard.php">
              <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'job_list.php' ? 'active' : '' ?>" href="job_list.php">
              <i class="fas fa-list"></i> Jobs
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'create_job.php' ? 'active' : '' ?>" href="create_job.php">
              <i class="fas fa-plus"></i> Create Job
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'report.php' ? 'active' : '' ?>" href="report.php">
              <i class="fas fa-chart-bar"></i> Reports
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'user_list.php' ? 'active' : '' ?>" href="user_list.php">
              <i class="fas fa-users"></i> Users
            </a>
          </li>
        </ul>
      </div>
    </div>
    <div class="col-md-9 col-lg-10">
      <div class="content">
        <!-- Remove the hardcoded job list that appears on every page -->
        <?php /* Remove this section
        <h2>Job List</h2>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>ID</th>
              <th>Job Name</th>
              <th>Client</th>
              <th>Start Date</th>
              <th>End Date</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <?php
            $sql = "SELECT * FROM jobs";
            $result = $pdo->query($sql);

            if ($result && $result->rowCount() > 0) {
              while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row["id"]) . "</td>";
                echo "<td>" . htmlspecialchars($row["job_name"]) . "</td>";
                echo "<td>" . htmlspecialchars($row["client"]) . "</td>";
                echo "<td>" . htmlspecialchars($row["start_date"]) . "</td>";
                echo "<td>" . htmlspecialchars($row["end_date"]) . "</td>";
                echo "<td>" . htmlspecialchars($row["status"]) . "</td>";
                echo "<td>";
                echo "<a href='view_job.php?id=" . htmlspecialchars($row["id"]) . "' class='btn btn-primary btn-sm'><i class='fas fa-eye'></i> View</a>";
                echo "<a href='edit_job.php?id=" . htmlspecialchars($row["id"]) . "' class='btn btn-secondary btn-sm'><i class='fas fa-edit'></i> Edit</a>";
                echo "<a href='delete_job.php?id=" . htmlspecialchars($row["id"]) . "' class='btn btn-danger btn-sm' onclick='return confirm(\"Are you sure you want to delete this job?\")'><i class='fas fa-trash'></i> Delete</a>";
                echo "</td>";
                echo "</tr>";
              }
            } else {
              echo "<tr><td colspan='7'>No jobs found</td></tr>";
            }
            ?>
          </tbody>
        </table>
        <a href="create_job.php" class="btn btn-success"><i class="fas fa-plus"></i> Create New Job</a>
        */ ?>
      </div>
    </div>
  </div>
</div>
</body>
</html>
