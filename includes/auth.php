<?php
// ฟังก์ชันเกี่ยวกับการยืนยันตัวตน

// ตรวจสอบว่าผู้ใช้ล็อกอินแล้วหรือไม่
function isLoggedIn() {
    return isset($_SESSION["user_id"]);
}

// ตรวจสอบว่าผู้ใช้เป็น adminหรือไม่
function isAdmin() {
    return isset($_SESSION["role"]) && $_SESSION["role"] === 'admin';
}

// บังคับให้ต้องล็อกอิน
function requireLogin() {
    if (!isLoggedIn()) {
        header("Location: login.php");
        exit;
    }
}

// บังคับให้ต้องเป็น admin
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        header("Location: dashboard.php");
        exit;
    }
}

// บันทึกความพยายามล็อกอินที่ล้มเหลว
function logFailedLogin($username) {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO login_attempts (username, ip_address, attempt_time) 
                          VALUES (?, ?, NOW())");
    $stmt->execute([$username, $_SERVER['REMOTE_ADDR']]);
}

// ตรวจสอบว่าถูกล็อกการล็อกอินหรือไม่
function isLoginLocked($username) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM login_attempts 
                          WHERE username = ? AND attempt_time > DATE_SUB(NOW(), INTERVAL 15 MINUTE)");
    $stmt->execute([$username]);
    return $stmt->fetchColumn() >= 5; // ล็อกหลังพยายาม 5 ครั้งใน 15 นาที
}