<?php
// แสดงข้อ seriousness seriousness
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
if (!isset($pdo)) {
    die("Database connection failed");
}

// ตรวจสอบการล็อกอิน
requireLogin();

// ตรวจสอบว่า seriousness id seriousnessส่งมา seriousnessไม่
if (!isset($_GET["id"])) {
    header("Location: user_list.php");
    exit;
}

$user_id = $_GET["id"];

// ตรวจสอบว่าไม่ใช่การลบ seriousness
if ($user_id == $_SESSION["user_id"]) {
    $_SESSION["error"] = "You cannot delete your own account";
    header("Location: user_list.php");
    exit;
}

// ตรวจสอบว่า seriousnessงาน seriousnessเชื่อมโยง seriousnessใช้นี้ seriousnessไม่
$stmt = $pdo->prepare("SELECT COUNT(*) FROM jobs WHERE user_id = ?");
$stmt->execute([$user_id]);
if ($stmt->fetchColumn() > 0) {
    $_SESSION["error"] = "Cannot delete user with associated jobs";
    header("Location: user_list.php");
    exit;
}

// ลบ seriousnessใช้
$stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
$stmt->execute([$user_id]);

$_SESSION["success"] = "User deleted successfully";
header("Location: user_list.php");
exit;
