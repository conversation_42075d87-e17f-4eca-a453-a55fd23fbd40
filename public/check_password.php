<?php
// ไฟล์สำหรับตรวจสอบรหัสผ่าน (ใช้เฉพาะดีบัก)
session_start();
require_once '../includes/db.php';

// ตรวจสอบว่าเป็น localhost เท่านั้น
if ($_SERVER['REMOTE_ADDR'] !== '127.0.0.1' && $_SERVER['REMOTE_ADDR'] !== '::1') {
    die("Access denied");
}

$username = 'admin'; // เปลี่ยนเป็นชื่อผู้ใช้ที่ต้องการตรวจสอบ
$password = 'admin123'; // เปลี่ยนเป็นรหัสผ่านที่คิดว่าถูกต้อง

$stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
$stmt->execute([$username]);
$user = $stmt->fetch();

if (!$user) {
    echo "User not found";
    exit;
}

echo "User found: " . $user['username'] . "<br>";
echo "Password hash in DB: " . $user['password_hash'] . "<br>";

// ทดสอบ password_verify
$verify_result = password_verify($password, $user['password_hash']);
echo "password_verify result: " . ($verify_result ? "TRUE" : "FALSE") . "<br>";

// สร้างรหัสผ่านใหม่เพื่อเปรียบเทียบ
$new_hash = password_hash($password, PASSWORD_DEFAULT);
echo "New hash for same password: " . $new_hash . "<br>";

// ถ้า password_verify ไม่ผ่าน ให้อัปเดตรหัสผ่านใหม่
if (!$verify_result) {
    echo "<form method='post'>";
    echo "<input type='hidden' name='update_password' value='1'>";
    echo "<button type='submit'>Update password hash in database</button>";
    echo "</form>";
    
    if (isset($_POST['update_password'])) {
        $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE username = ?");
        $stmt->execute([$new_hash, $username]);
        echo "Password hash updated!";
    }
}