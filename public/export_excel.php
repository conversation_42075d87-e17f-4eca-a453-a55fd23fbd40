<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// ตรวจสอบการล็อกอิน
requireLogin();
header("Content-Type: application/vnd.ms-excel");
header("Content-Disposition: attachment; filename=report.xls");

$where = [];
$params = [];

if (!empty($_GET["status"])) {
    $where[] = "status = ?";
    $params[] = $_GET["status"];
}
if (!empty($_GET["user_id"])) {
    $where[] = "user_id = ?";
    $params[] = $_GET["user_id"];
}

$query = "SELECT jobs.*, users.username FROM jobs JOIN users ON users.id = jobs.user_id";
if ($where) {
    $query .= " WHERE " . implode(" AND ", $where);
}
$stmt = $pdo->prepare($query);
$stmt->execute($params);

echo "Job Number\tCustomer\tContainer\tStatus\tUser\n";
foreach ($stmt as $row) {
    echo "{$row["job_number"]}\t{$row["customer_name"]}\t{$row["container_no"]}\t{$row["status"]}\t{$row["username"]}\n";
}
