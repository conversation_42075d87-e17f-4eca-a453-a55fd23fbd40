<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// ตรวจสอบประเภทการส่งออก
$export_type = $_GET['type'] ?? 'csv';

// สร้างคำสั่ง SQL การกรอง
$where = [];
$params = [];

// Filter by status
if (!empty($_GET["status"])) {
    $where[] = "status = ?";
    $params[] = $_GET["status"];
}

// Filter by user
if (!empty($_GET["user"])) {
    $where[] = "user_id = ?";
    $params[] = $_GET["user"];
}

// Filter by date range
if (!empty($_GET["start_date"])) {
    $where[] = "job_date >= ?";
    $params[] = $_GET["start_date"];
}
if (!empty($_GET["end_date"])) {
    $where[] = "job_date <= ?";
    $params[] = $_GET["end_date"];
}

// Build query
$sql = "SELECT jobs.*, users.username FROM jobs JOIN users ON users.id = jobs.user_id";
if ($where) $sql .= " WHERE " . implode(" AND ", $where);
$sql .= " ORDER BY job_date DESC";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$data = $stmt->fetchAll();

// ส่งออกข้อมูลตามประเภท
if ($export_type == 'pdf') {
    // สร้าง HTML PDF
    $html = '<h1>Container System Report</h1>';
    $html .= '<table border="1" cellpadding="5" cellspacing="0" width="100%">';
    $html .= '<tr style="background-color: #f2f2f2;">';
    $html .= '<th>Job#</th><th>Customer</th><th>Container No.</th><th>Status</th><th>Return Date</th><th>User</th>';
    $html .= '</tr>';
    
    foreach ($data as $row) {
        $html .= '<tr>';
        $html .= '<td>' . htmlspecialchars($row["job_number"]) . '</td>';
        $html .= '<td>' . htmlspecialchars($row["customer_name"]) . '</td>';
        $html .= '<td>' . htmlspecialchars($row["container_no"]) . '</td>';
        $html .= '<td>' . htmlspecialchars($row["status"]) . '</td>';
        $html .= '<td>' . htmlspecialchars($row["final_return_date"] ?? 'N/A') . '</td>';
        $html .= '<td>' . htmlspecialchars($row["username"]) . '</td>';
        $html .= '</tr>';
    }
    
    $html .= '</table>';
    
    // ส่งออกเป็น PDF
    exportToPDF($html, 'container_report.pdf');
} else {
    // เตรียมข้อมูล CSV
    $csv_data = [];
    foreach ($data as $row) {
        $csv_data[] = [
            'Job Number' => $row['job_number'],
            'Customer' => $row['customer_name'],
            'Container No' => $row['container_no'],
            'Container Size' => $row['container_size'],
            'Status' => $row['status'],
            'Return Date' => $row['final_return_date'] ?? 'N/A',
            'User' => $row['username']
        ];
    }
    
    // ส่งออกเป็น CSV
    exportToCSV($csv_data, 'container_report.csv');
}
